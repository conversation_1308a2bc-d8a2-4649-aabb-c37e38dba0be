# config_fixer.py - Fix and validate config.json
import json
import os

def create_clean_config():
    """Create a clean, properly formatted config.json"""
    clean_config = {
        "email": {
            "smtp_server": "smtp.gmail.com",
            "smtp_port": 587,
            "sender_email": "<EMAIL>",
            "sender_password": "your_app_password",
            "recipient_email": "<EMAIL>"
        }
    }
    
    try:
        with open('config.json', 'w') as f:
            json.dump(clean_config, f, indent=4)
        print("✅ Created clean config.json")
        return True
    except Exception as e:
        print(f"❌ Failed to create config.json: {e}")
        return False

def validate_config():
    """Validate the config.json file"""
    try:
        with open('config.json', 'r') as f:
            config = json.load(f)
        
        # Check structure
        if 'email' not in config:
            print("❌ Missing 'email' section")
            return False
        
        email_config = config['email']
        required_fields = ['smtp_server', 'smtp_port', 'sender_email', 'sender_password', 'recipient_email']
        
        missing_fields = []
        for field in required_fields:
            if field not in email_config:
                missing_fields.append(field)
        
        if missing_fields:
            print(f"❌ Missing fields: {', '.join(missing_fields)}")
            return False
        
        # Check if default values are still present
        if email_config['sender_email'] == '<EMAIL>':
            print("⚠️  Please update sender_email with your actual Gmail address")
        
        if email_config['sender_password'] == 'your_app_password':
            print("⚠️  Please update sender_password with your Gmail App Password")
        
        if email_config['recipient_email'] == '<EMAIL>':
            print("⚠️  Please update recipient_email with the email to receive alerts")
        
        print("✅ config.json structure is valid")
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ JSON syntax error: {e}")
        print("Line", e.lineno, "Column", e.colno)
        return False
    except FileNotFoundError:
        print("❌ config.json not found")
        return False
    except Exception as e:
        print(f"❌ Error reading config: {e}")
        return False

def show_config_template():
    """Show a properly formatted config template"""
    print("\n📝 CORRECT CONFIG.JSON FORMAT:")
    print("=" * 40)
    
    template = '''{
    "email": {
        "smtp_server": "smtp.gmail.com",
        "smtp_port": 587,
        "sender_email": "<EMAIL>",
        "sender_password": "abcd efgh ijkl mnop",
        "recipient_email": "<EMAIL>"
    }
}'''
    
    print(template)
    print("\n" + "=" * 40)
    print("⚠️  Important Notes:")
    print("1. Use double quotes (\") not single quotes (')")
    print("2. No comma after the last item in each section")
    print("3. smtp_port should be a number (no quotes)")
    print("4. Replace '<EMAIL>' with your actual email")
    print("5. Replace 'abcd efgh ijkl mnop' with your Gmail App Password")

def backup_and_fix():
    """Backup existing config and create a new one"""
    if os.path.exists('config.json'):
        # Create backup
        try:
            with open('config.json', 'r') as f:
                content = f.read()
            
            with open('config_backup.json', 'w') as f:
                f.write(content)
            
            print("💾 Backed up existing config to 'config_backup.json'")
        except Exception as e:
            print(f"⚠️  Could not backup existing config: {e}")
    
    return create_clean_config()

def interactive_config_creator():
    """Create config file interactively"""
    print("\n🛠️  INTERACTIVE CONFIG CREATOR")
    print("=" * 40)
    
    print("Let's create your config.json step by step...")
    print("(Press Enter to use default values shown in brackets)")
    
    # Get email details
    sender_email = input("\n📧 Your Gmail address: ").strip()
    if not sender_email:
        sender_email = "<EMAIL>"
    
    sender_password = input("🔑 Your Gmail App Password (16 characters): ").strip()
    if not sender_password:
        sender_password = "your_app_password"
    
    recipient_email = input(f"📨 Alert recipient email [{sender_email}]: ").strip()
    if not recipient_email:
        recipient_email = sender_email
    
    smtp_server = input("🌐 SMTP Server [smtp.gmail.com]: ").strip()
    if not smtp_server:
        smtp_server = "smtp.gmail.com"
    
    smtp_port = input("🔌 SMTP Port [587]: ").strip()
    if not smtp_port:
        smtp_port = 587
    else:
        try:
            smtp_port = int(smtp_port)
        except ValueError:
            smtp_port = 587
    
    # Create config
    config = {
        "email": {
            "smtp_server": smtp_server,
            "smtp_port": smtp_port,
            "sender_email": sender_email,
            "sender_password": sender_password,
            "recipient_email": recipient_email
        }
    }
    
    # Save config
    try:
        with open('config.json', 'w') as f:
            json.dump(config, f, indent=4)
        
        print("\n✅ Successfully created config.json!")
        
        # Validate the created config
        if validate_config():
            print("✅ Config validation passed!")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to create config: {e}")
        return False

def main():
    """Main config fixer function"""
    print("🔧 CONFIG.JSON FIXER & VALIDATOR")
    print("=" * 40)
    
    print("\nWhat would you like to do?")
    print("1. Validate existing config.json")
    print("2. Show correct config format")
    print("3. Create clean config (backup existing)")
    print("4. Interactive config creator")
    print("5. Exit")
    
    while True:
        choice = input("\nEnter your choice (1-5): ").strip()
        
        if choice == '1':
            print("\n🔍 Validating config.json...")
            validate_config()
            break
            
        elif choice == '2':
            show_config_template()
            break
            
        elif choice == '3':
            print("\n🔄 Creating clean config...")
            if backup_and_fix():
                print("✅ Clean config created successfully!")
                show_config_template()
            break
            
        elif choice == '4':
            if interactive_config_creator():
                print("\n🎉 All done! Your config.json is ready.")
            break
            
        elif choice == '5':
            print("👋 Goodbye!")
            break
            
        else:
            print("❌ Invalid choice. Please enter 1-5.")

if __name__ == "__main__":
    main()
