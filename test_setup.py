# test_setup.py - Setup verification script
import sys
import subprocess
import requests
from bs4 import BeautifulSoup
import json
import os

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major >= 3 and version.minor >= 7:
        print(f"✅ Python {version.major}.{version.minor} is compatible")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor} is too old. Need Python 3.7+")
        return False

def check_packages():
    """Check if required packages are installed"""
    required_packages = ['requests', 'beautifulsoup4', 'schedule']
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'beautifulsoup4':
                import bs4
            else:
                __import__(package)
            print(f"✅ {package} is installed")
        except ImportError:
            print(f"❌ {package} is missing")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n🔧 Install missing packages with:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    return True

def test_internet_connection():
    """Test internet connectivity"""
    try:
        response = requests.get("https://httpbin.org/get", timeout=10)
        if response.status_code == 200:
            print("✅ Internet connection working")
            return True
    except Exception as e:
        print(f"❌ Internet connection failed: {e}")
        return False

def test_amazon_access():
    """Test if Amazon is accessible"""
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        response = requests.get("https://www.amazon.in", headers=headers, timeout=10)
        if response.status_code == 200:
            print("✅ Amazon access working")
            return True
        else:
            print(f"⚠️  Amazon returned status code: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Amazon access failed: {e}")
        return False

def test_flipkart_access():
    """Test if Flipkart is accessible"""
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        response = requests.get("https://www.flipkart.com", headers=headers, timeout=10)
        if response.status_code == 200:
            print("✅ Flipkart access working")
            return True
        else:
            print(f"⚠️  Flipkart returned status code: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Flipkart access failed: {e}")
        return False

def check_config_file():
    """Check if config.json exists and is valid"""
    if not os.path.exists('config.json'):
        print("⚠️  config.json not found. Creating default config...")
        default_config = {
            "email": {
                "smtp_server": "smtp.gmail.com",
                "smtp_port": 587,
                "sender_email": "<EMAIL>",
                "sender_password": "your_app_password",
                "recipient_email": "<EMAIL>"
            }
        }
        with open('config.json', 'w') as f:
            json.dump(default_config, f, indent=4)
        print("⚠️  Please update config.json with your email details")
        return False
    
    try:
        with open('config.json', 'r') as f:
            config = json.load(f)
        
        email_config = config.get('email', {})
        if email_config.get('sender_email') == '<EMAIL>':
            print("⚠️  Please update config.json with your actual email")
            return False
        
        print("✅ config.json exists and looks good")
        return True
    except Exception as e:
        print(f"❌ config.json is invalid: {e}")
        return False

def create_requirements_file():
    """Create requirements.txt if it doesn't exist"""
    if not os.path.exists('requirements.txt'):
        requirements = """requests==2.31.0
beautifulsoup4==4.12.2
schedule==1.2.0
lxml==4.9.3
"""
        with open('requirements.txt', 'w') as f:
            f.write(requirements)
        print("✅ Created requirements.txt")

def main():
    """Main setup test function"""
    print("🧪 PRICE TRACKER SETUP TEST")
    print("=" * 40)
    
    tests_passed = 0
    total_tests = 6
    
    # Test 1: Python Version
    if check_python_version():
        tests_passed += 1
    
    print()
    
    # Test 2: Required Packages
    if check_packages():
        tests_passed += 1
    
    print()
    
    # Test 3: Internet Connection
    if test_internet_connection():
        tests_passed += 1
    
    print()
    
    # Test 4: Amazon Access
    if test_amazon_access():
        tests_passed += 1
    
    print()
    
    # Test 5: Flipkart Access
    if test_flipkart_access():
        tests_passed += 1
    
    print()
    
    # Test 6: Config File
    if check_config_file():
        tests_passed += 1
    
    print()
    
    # Create requirements.txt
    create_requirements_file()
    
    print("=" * 40)
    print(f"📊 RESULTS: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! You're ready to run the price tracker!")
        print("\n🚀 Next steps:")
        print("1. Update config.json with your Gmail details")
        print("2. Run: python price_tracker.py")
    else:
        print("⚠️  Please fix the issues above before running the tracker")
        
        if tests_passed < 3:
            print("\n🔧 Quick fixes:")
            print("1. Install packages: pip install requests beautifulsoup4 schedule")
            print("2. Check your internet connection")
            print("3. Update config.json with your email")

if __name__ == "__main__":
    main()
