import requests
from bs4 import BeautifulSoup
import smtplib
import json
import time
import logging
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON>ultipart
from datetime import datetime
import os
from typing import Dict, List, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('price_tracker.log'),
        logging.StreamHandler()
    ]
)

class PriceTracker:
    def __init__(self):
        self.products = []
        self.config = self.load_config()
        self.session = requests.Session()
        
        # Headers to avoid blocking
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        }
        self.session.headers.update(self.headers)

    def load_config(self) -> Dict:
        """Load email configuration"""
        try:
            with open('config.json', 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            return {
                "email": {
                    "smtp_server": "smtp.gmail.com",
                    "smtp_port": 587,
                    "sender_email": "<EMAIL>",
                    "sender_password": "your_app_password",
                    "recipient_email": "<EMAIL>"
                }
            }

    def save_config(self):
        """Save configuration to file"""
        with open('config.json', 'w') as f:
            json.dump(self.config, f, indent=4)

    def load_products(self):
        """Load tracked products from file"""
        try:
            with open('products.json', 'r') as f:
                self.products = json.load(f)
        except FileNotFoundError:
            self.products = []

    def save_products(self):
        """Save tracked products to file"""
        with open('products.json', 'w') as f:
            json.dump(self.products, f, indent=4)

    def extract_amazon_price(self, soup: BeautifulSoup) -> Optional[float]:
        """Extract price from Amazon product page"""
        price_selectors = [
            '.a-price-whole',
            '.a-offscreen',
            '#priceblock_dealprice',
            '#priceblock_ourprice',
            '.a-price .a-offscreen'
        ]
        
        for selector in price_selectors:
            price_element = soup.select_one(selector)
            if price_element:
                price_text = price_element.get_text().strip()
                # Remove currency symbols and commas
                price_clean = ''.join(filter(str.isdigit, price_text.replace('.', '')))
                if price_clean:
                    return float(price_clean) / 100  # Convert paise to rupees
        
        return None

    def extract_flipkart_price(self, soup: BeautifulSoup) -> Optional[float]:
        """Extract price from Flipkart product page"""
        price_selectors = [
            '._30jeq3._16Jk6d',
            '._1_WHN1',
            '.CEmiEU .Nx9bqj',
            '._16Jk6d'
        ]
        
        for selector in price_selectors:
            price_element = soup.select_one(selector)
            if price_element:
                price_text = price_element.get_text().strip()
                # Remove currency symbols and commas
                price_clean = ''.join(filter(str.isdigit, price_text.replace(',', '')))
                if price_clean:
                    return float(price_clean)
        
        return None

    def get_product_price(self, url: str, platform: str) -> Optional[float]:
        """Fetch current price of a product"""
        try:
            response = self.session.get(url, timeout=10)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            if platform.lower() == 'amazon':
                return self.extract_amazon_price(soup)
            elif platform.lower() == 'flipkart':
                return self.extract_flipkart_price(soup)
            else:
                logging.warning(f"Unsupported platform: {platform}")
                return None
                
        except requests.RequestException as e:
            logging.error(f"Error fetching price for {url}: {e}")
            return None
        except Exception as e:
            logging.error(f"Unexpected error for {url}: {e}")
            return None

    def add_product(self, name: str, url: str, platform: str, target_price: float):
        """Add a product to track"""
        current_price = self.get_product_price(url, platform)
        
        if current_price is None:
            logging.error(f"Could not fetch current price for {name}")
            return False
        
        product = {
            "name": name,
            "url": url,
            "platform": platform.lower(),
            "target_price": target_price,
            "current_price": current_price,
            "lowest_price": current_price,
            "added_date": datetime.now().isoformat(),
            "last_checked": datetime.now().isoformat(),
            "price_history": [{"date": datetime.now().isoformat(), "price": current_price}]
        }
        
        self.products.append(product)
        self.save_products()
        
        logging.info(f"Added product: {name} - Current Price: ₹{current_price}")
        return True

    def send_email_alert(self, product: Dict, old_price: float, new_price: float):
        """Send email alert when price drops"""
        try:
            msg = MIMEMultipart()
            msg['From'] = self.config['email']['sender_email']
            msg['To'] = self.config['email']['recipient_email']
            msg['Subject'] = f"🔥 Price Drop Alert: {product['name']}"
            
            body = f"""
            Great news! The price has dropped for the product you're tracking:
            
            Product: {product['name']}
            Platform: {product['platform'].title()}
            
            Previous Price: ₹{old_price}
            Current Price: ₹{new_price}
            You Save: ₹{old_price - new_price} ({((old_price - new_price) / old_price * 100):.1f}%)
            
            Target Price: ₹{product['target_price']}
            {'✅ TARGET PRICE REACHED!' if new_price <= product['target_price'] else '⏳ Still above target price'}
            
            Product Link: {product['url']}
            
            Happy Shopping!
            Price Tracker Bot 🤖
            """
            
            msg.attach(MIMEText(body, 'plain'))
            
            server = smtplib.SMTP(self.config['email']['smtp_server'], self.config['email']['smtp_port'])
            server.starttls()
            server.login(self.config['email']['sender_email'], self.config['email']['sender_password'])
            
            text = msg.as_string()
            server.sendmail(self.config['email']['sender_email'], self.config['email']['recipient_email'], text)
            server.quit()
            
            logging.info(f"Price drop alert sent for {product['name']}")
            
        except Exception as e:
            logging.error(f"Failed to send email alert: {e}")

    def check_prices(self):
        """Check prices for all tracked products"""
        logging.info(f"Starting price check for {len(self.products)} products...")
        
        for i, product in enumerate(self.products):
            try:
                logging.info(f"Checking price for: {product['name']}")
                
                current_price = self.get_product_price(product['url'], product['platform'])
                
                if current_price is None:
                    logging.warning(f"Could not fetch price for {product['name']}")
                    continue
                
                old_price = product['current_price']
                product['current_price'] = current_price
                product['last_checked'] = datetime.now().isoformat()
                
                # Update price history
                product['price_history'].append({
                    "date": datetime.now().isoformat(),
                    "price": current_price
                })
                
                # Keep only last 30 entries in history
                if len(product['price_history']) > 30:
                    product['price_history'] = product['price_history'][-30:]
                
                # Update lowest price
                if current_price < product['lowest_price']:
                    product['lowest_price'] = current_price
                
                # Check for price drop (at least 1% or target reached)
                price_drop_threshold = old_price * 0.01  # 1% threshold
                
                if (current_price < old_price - price_drop_threshold) or (current_price <= product['target_price'] and old_price > product['target_price']):
                    self.send_email_alert(product, old_price, current_price)
                
                logging.info(f"Price updated: {product['name']} - ₹{old_price} → ₹{current_price}")
                
                # Add delay between requests to be respectful
                time.sleep(2)
                
            except Exception as e:
                logging.error(f"Error checking price for {product['name']}: {e}")
                continue
        
        self.save_products()
        logging.info("Price check completed!")

    def remove_product(self, index: int) -> bool:
        """Remove a product from tracking"""
        if 0 <= index < len(self.products):
            removed = self.products.pop(index)
            self.save_products()
            logging.info(f"Removed product: {removed['name']}")
            return True
        return False

    def display_products(self):
        """Display all tracked products"""
        if not self.products:
            print("No products are being tracked.")
            return
        
        print("\n" + "="*80)
        print("📊 TRACKED PRODUCTS")
        print("="*80)
        
        for i, product in enumerate(self.products):
            status = "🎯 TARGET REACHED" if product['current_price'] <= product['target_price'] else "⏳ Tracking"
            
            print(f"\n[{i+1}] {product['name']}")
            print(f"    Platform: {product['platform'].title()}")
            print(f"    Current Price: ₹{product['current_price']}")
            print(f"    Target Price: ₹{product['target_price']}")
            print(f"    Lowest Price: ₹{product['lowest_price']}")
            print(f"    Status: {status}")
            print(f"    Last Checked: {product['last_checked']}")

def main():
    """Main function with CLI interface"""
    tracker = PriceTracker()
    tracker.load_products()
    
    print("🛒 E-commerce Price Tracker Bot")
    print("================================")
    
    while True:
        print("\n1. Add Product")
        print("2. View Tracked Products")
        print("3. Check Prices Now")
        print("4. Remove Product")
        print("5. Configure Email")
        print("6. Exit")
        
        choice = input("\nEnter your choice (1-6): ").strip()
        
        if choice == '1':
            name = input("Product Name: ").strip()
            url = input("Product URL: ").strip()
            platform = input("Platform (amazon/flipkart): ").strip()
            
            try:
                target_price = float(input("Target Price (₹): ").strip())
                if tracker.add_product(name, url, platform, target_price):
                    print(f"✅ Successfully added {name} to tracking!")
                else:
                    print("❌ Failed to add product. Please check the URL and try again.")
            except ValueError:
                print("❌ Invalid price format!")
        
        elif choice == '2':
            tracker.display_products()
        
        elif choice == '3':
            print("🔍 Checking prices...")
            tracker.check_prices()
            print("✅ Price check completed!")
        
        elif choice == '4':
            tracker.display_products()
            if tracker.products:
                try:
                    index = int(input("Enter product number to remove: ")) - 1
                    if tracker.remove_product(index):
                        print("✅ Product removed successfully!")
                    else:
                        print("❌ Invalid product number!")
                except ValueError:
                    print("❌ Invalid input!")
        
        elif choice == '5':
            print("\n📧 Email Configuration")
            email = input("Your Email: ").strip()
            password = input("App Password (for Gmail): ").strip()
            recipient = input("Recipient Email: ").strip()
            
            tracker.config['email'].update({
                'sender_email': email,
                'sender_password': password,
                'recipient_email': recipient
            })
            tracker.save_config()
            print("✅ Email configuration saved!")
        
        elif choice == '6':
            print("👋 Goodbye! Happy shopping!")
            break
        
        else:
            print("❌ Invalid choice!")

if __name__ == "__main__":
    main()
